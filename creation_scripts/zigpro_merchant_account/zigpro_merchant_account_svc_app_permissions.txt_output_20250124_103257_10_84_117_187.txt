------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2025-01-24 10:32:57.598430 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2025-01-24 10:32:57.621462+08:00 | 2025-01-24 10:32:57.574167+08:00 |  ucdg_aseem  |  ucdg_aseem  | zigpro_payment_gateway |  10.84.117.187   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) and write (INSERT, UPDATE, DELETE) permissions on all tables in the schema to readwrite_user
GRANT USAGE ON SCHEMA zigpro_merchant_account TO zigpro_merchant_account_svc_app_rw;
GRANT Executed successfully.

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA zigpro_merchant_account TO zigpro_merchant_account_svc_app_rw;
GRANT granted successfully.

GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA zigpro_merchant_account TO zigpro_merchant_account_svc_app_rw;
GRANT granted successfully.

GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA zigpro_merchant_account TO zigpro_merchant_account_svc_app_rw;
GRANT granted successfully.

-- Set default privileges for future tables created in the schema
GRANT zigpro_merchant_account_lb_ap TO cdg_admin;
GRANT ROLE Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
  GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO zigpro_merchant_account_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
  GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO zigpro_merchant_account_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
  GRANT EXECUTE ON FUNCTIONS TO zigpro_merchant_account_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

+----------------------------+
|       run_ended_time       |
+----------------------------+
| 2025-01-24 10:33:00.991743 |
+----------------------------+
------------------------------------------------------
Stopped at Local Timestamp: 2025-01-24 10:33:00.991743
------------------------------------------------------
