

> -- Step 1: Create database for zigpro
CREATE DATABASE zigpro_payment_gateway

0 row(s) modified.

> -- Step 2: Create schemas for zigpro microservices
CREATE SCHEMA zigpro_payment_orchestrator

0 row(s) modified.

> CREATE SCHEMA zigpro_merchant_account

0 row(s) modified.

> CREATE SCHEMA zigpro_adyen_adapter

0 row(s) modified.

> CREATE SCHEMA zigpro_notification

0 row(s) modified.

> CREATE SCHEMA zigpro_routing_engine

0 row(s) modified.

> CREATE SCHEMA zigpro_scheduler_orchestrator

0 row(s) modified.

> CREATE SCHEMA zigpro_portal_user

0 row(s) modified.

