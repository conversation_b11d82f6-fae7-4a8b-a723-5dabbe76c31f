-- Grant read (SELECT) permissions on all tables in the schema to readonly_user

GRANT CONNECT ON DATABASE zigpro_payment_gateway TO testing_zigpro_ro;

GRANT USAGE ON SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;


GRANT USAGE ON SCHEMA zigpro_merchant_account TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_merchant_account TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
   
 
GRANT USAGE ON SCHEMA zigpro_adyen_adapter TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_adyen_adapter TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_adyen_adapter FOR ROLE zigpro_adyen_adapter_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_adyen_adapter FOR ROLE zigpro_adyen_adapter_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
   
GRANT USAGE ON SCHEMA zigpro_notification TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_notification TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_notification FOR ROLE zigpro_notification_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_notification FOR ROLE zigpro_notification_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
   
GRANT USAGE ON SCHEMA zigpro_routing_engine TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_routing_engine TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
   
GRANT USAGE ON SCHEMA zigpro_scheduler_orchestrator TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_scheduler_orchestrator TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
   
GRANT USAGE ON SCHEMA zigpro_portal_user TO testing_zigpro_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_portal_user TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;