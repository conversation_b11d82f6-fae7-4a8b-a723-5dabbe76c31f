------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2025-01-24 10:24:20.879389 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2025-01-24 10:24:20.902159+08:00 | 2025-01-24 10:24:20.850533+08:00 |  ucdg_aseem  |  ucdg_aseem  | zigpro_payment_gateway |  10.84.117.187   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) permissions on all tables in the schema to readonly_user

GRANT CONNECT ON DATABASE zigpro_payment_gateway TO testing_zigpro_ro;
GRANT Executed successfully.

grant zigpro_payment_orchestrator_lb_ap to ucdg_aseem;
GRANT ROLE granted successfully.

grant zigpro_merchant_account_lb_ap to ucdg_aseem;
GRANT ROLE granted successfully.

grant zigpro_adyen_adapter_lb_ap to ucdg_aseem;
GRANT ROLE granted successfully.

grant zigpro_notification_lb_ap to ucdg_aseem;
GRANT ROLE granted successfully.

grant zigpro_routing_engine_lb_ap to ucdg_aseem;
GRANT ROLE granted successfully.

grant zigpro_scheduler_orchestrator_lb_ap to ucdg_aseem;
GRANT ROLE granted successfully.

Error executing SQL statement: grant zigpro_portal_user_lb_ap to ucdg_aseem;
Error message: role "zigpro_portal_user_lb_ap" does not exist


Rollback done! Aborting the remaining script execution.
