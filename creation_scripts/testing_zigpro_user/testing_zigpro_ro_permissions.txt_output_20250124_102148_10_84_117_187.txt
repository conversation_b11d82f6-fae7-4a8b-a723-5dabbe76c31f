------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2025-01-24 10:21:48.020353 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2025-01-24 10:21:48.047358+08:00 | 2025-01-24 10:21:47.993497+08:00 |  ucdg_aseem  |  ucdg_aseem  | zigpro_payment_gateway |  10.84.117.187   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) permissions on all tables in the schema to readonly_user

GRANT CONNECT ON DATABASE zigpro_payment_gateway TO testing_zigpro_ro;
GRANT Executed successfully.

GRANT USAGE ON SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
GRANT granted successfully.

Error executing SQL statement: ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
Error message: must be member of role "zigpro_payment_orchestrator_lb_ap"


Rollback done! Aborting the remaining script execution.
