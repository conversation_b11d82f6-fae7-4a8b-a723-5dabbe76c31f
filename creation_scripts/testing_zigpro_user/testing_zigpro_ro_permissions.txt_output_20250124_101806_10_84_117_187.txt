------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2025-01-24 10:18:06.488041 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2025-01-24 10:18:06.510675+08:00 | 2025-01-24 10:18:06.459699+08:00 |  ucdg_aseem  |  ucdg_aseem  | zigpro_payment_gateway |  10.84.117.187   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) permissions on all tables in the schema to readonly_user

GRANT CONNECT ON DATABASE zigpro_payment_gateway TO testing_zigpro_ro;
GRANT Executed successfully.

Error executing SQL statement: GRANT USAGE ON SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
Error message: schema "zigpro_payment_orchestrator" does not exist


Rollback done! Aborting the remaining script execution.
