------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2024-10-21 12:32:48.633035 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2024-10-21 12:32:48.654395+08:00 | 2024-10-21 12:32:48.608923+08:00 | ucdg_nelson  | ucdg_nelson  | zigpro_payment_gateway |  10.82.114.215   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) permissions on all tables in the schema to readonly_user

GRANT CONNECT ON DATABASE zigpro_payment_gateway TO testing_zigpro_ro;
GRANT Executed successfully.

GRANT USAGE ON SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_payment_orchestrator TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

GRANT USAGE ON SCHEMA zigpro_merchant_account TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_merchant_account TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_merchant_account FOR ROLE zigpro_merchant_account_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

GRANT USAGE ON SCHEMA zigpro_adyen_adapter TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_adyen_adapter TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_adyen_adapter FOR ROLE zigpro_adyen_adapter_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_adyen_adapter FOR ROLE zigpro_adyen_adapter_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

GRANT USAGE ON SCHEMA zigpro_notification TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_notification TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_notification FOR ROLE zigpro_notification_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_notification FOR ROLE zigpro_notification_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

GRANT USAGE ON SCHEMA zigpro_routing_engine TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_routing_engine TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

GRANT USAGE ON SCHEMA zigpro_scheduler_orchestrator TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_scheduler_orchestrator TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

GRANT USAGE ON SCHEMA zigpro_portal_user TO testing_zigpro_ro;
GRANT granted successfully.

GRANT SELECT ON ALL TABLES IN SCHEMA zigpro_portal_user TO testing_zigpro_ro;
GRANT granted successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
   GRANT SELECT ON TABLES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
   GRANT SELECT ON SEQUENCES TO testing_zigpro_ro;
ALTER DEFAULT PRIVILEGES Executed successfully.

+----------------------------+
|       run_ended_time       |
+----------------------------+
| 2024-10-21 12:32:51.317455 |
+----------------------------+
------------------------------------------------------
Stopped at Local Timestamp: 2024-10-21 12:32:51.317455
------------------------------------------------------
