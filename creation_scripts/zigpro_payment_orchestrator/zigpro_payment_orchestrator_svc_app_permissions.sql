-- Grant read (SELECT) and write (INSERT, UPDATE, DELETE) permissions on all tables in the schema to readwrite_user
GRANT USAGE ON SCHEMA zigpro_payment_orchestrator TO zigpro_payment_orchestrator_app_rw;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA zigpro_payment_orchestrator TO zigpro_payment_orchestrator_app_rw;
GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA zigpro_payment_orchestrator TO zigpro_payment_orchestrator_app_rw;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA zigpro_payment_orchestrator TO zigpro_payment_orchestrator_app_rw;

-- Set default privileges for future tables created in the schema
GRANT zigpro_payment_orchestrator_lb_ap TO cdg_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
  GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO zigpro_payment_orchestrator_app_rw;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
  GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO zigpro_payment_orchestrator_app_rw;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_payment_orchestrator FOR ROLE zigpro_payment_orchestrator_lb_ap
  GRANT EXECUTE ON FUNCTIONS TO zigpro_payment_orchestrator_app_rw;
