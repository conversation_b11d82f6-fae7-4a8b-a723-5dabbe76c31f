------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2024-10-21 12:27:58.965715 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2024-10-21 12:27:58.985582+08:00 | 2024-10-21 12:27:58.940733+08:00 | ucdg_nelson  | ucdg_nelson  | zigpro_payment_gateway |  10.82.114.215   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) and write (INSERT, UPDATE, DELETE) permissions on all tables in the schema to readwrite_user
GRANT USAGE ON SCHEMA zigpro_portal_user TO zigpro_portal_user_svc_app_rw;
GRANT Executed successfully.

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA zigpro_portal_user TO zigpro_portal_user_svc_app_rw;
GRANT granted successfully.

GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA zigpro_portal_user TO zigpro_portal_user_svc_app_rw;
GRANT granted successfully.

GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA zigpro_portal_user TO zigpro_portal_user_svc_app_rw;
GRANT granted successfully.

-- Set default privileges for future tables created in the schema
GRANT zigpro_portal_user_lb_ap TO cdg_admin;
GRANT ROLE Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
  GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO zigpro_portal_user_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
  GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO zigpro_portal_user_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_portal_user FOR ROLE zigpro_portal_user_lb_ap
  GRANT EXECUTE ON FUNCTIONS TO zigpro_portal_user_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

+----------------------------+
|       run_ended_time       |
+----------------------------+
| 2024-10-21 12:28:01.443633 |
+----------------------------+
------------------------------------------------------
Stopped at Local Timestamp: 2024-10-21 12:28:01.443633
------------------------------------------------------
