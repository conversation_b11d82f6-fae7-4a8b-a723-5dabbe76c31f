-- <PERSON> read (SELECT) and write (INSERT, UPDATE, DELETE) permissions on all tables in the schema to readwrite_user
GRANT USAGE ON SCHEMA zigpro_routing_engine TO zigpro_routing_engine_svc_app_rw;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA zigpro_routing_engine TO zigpro_routing_engine_svc_app_rw;
GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA zigpro_routing_engine TO zigpro_routing_engine_svc_app_rw;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA zigpro_routing_engine TO zigpro_routing_engine_svc_app_rw;

-- Set default privileges for future tables created in the schema
GRANT zigpro_routing_engine_lb_ap TO cdg_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
  GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO zigpro_routing_engine_svc_app_rw;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
  GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO zigpro_routing_engine_svc_app_rw;
ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_routing_engine FOR ROLE zigpro_routing_engine_lb_ap
  GRANT EXECUTE ON FUNCTIONS TO zigpro_routing_engine_svc_app_rw;
