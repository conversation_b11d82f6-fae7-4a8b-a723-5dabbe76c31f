------------------------------------------------------
Started at Local Timestamp:
+----------------------------+
|         local_time         |
+----------------------------+
| 2025-01-24 10:38:33.389356 |
+----------------------------+
------------------------------------------------------
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
|         clock_timestamp          |        current_timestamp         | current_user | session_user |    current_database    | inet_server_addr | inet_server_port | inet_client_addr |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
| 2025-01-24 10:38:33.411716+08:00 | 2025-01-24 10:38:33.362464+08:00 |  ucdg_aseem  |  ucdg_aseem  | zigpro_payment_gateway |  10.84.117.187   |       5432       |    10.18.1.20    |
+----------------------------------+----------------------------------+--------------+--------------+------------------------+------------------+------------------+------------------+
------------------------------------------------------
-- Grant read (SELECT) and write (INSERT, UPDATE, DELETE) permissions on all tables in the schema to readwrite_user
GRANT USAGE ON SCHEMA zigpro_scheduler_orchestrator TO zigpro_scheduler_orchestrator_svc_app_rw;
GRANT Executed successfully.

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA zigpro_scheduler_orchestrator TO zigpro_scheduler_orchestrator_svc_app_rw;
GRANT granted successfully.

GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA zigpro_scheduler_orchestrator TO zigpro_scheduler_orchestrator_svc_app_rw;
GRANT granted successfully.

GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA zigpro_scheduler_orchestrator TO zigpro_scheduler_orchestrator_svc_app_rw;
GRANT granted successfully.

-- Set default privileges for future tables created in the schema
GRANT zigpro_scheduler_orchestrator_lb_ap TO cdg_admin;
GRANT ROLE Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
  GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO zigpro_scheduler_orchestrator_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
  GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO zigpro_scheduler_orchestrator_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

ALTER DEFAULT PRIVILEGES IN SCHEMA zigpro_scheduler_orchestrator FOR ROLE zigpro_scheduler_orchestrator_lb_ap
  GRANT EXECUTE ON FUNCTIONS TO zigpro_scheduler_orchestrator_svc_app_rw;
ALTER DEFAULT PRIVILEGES Executed successfully.

+----------------------------+
|       run_ended_time       |
+----------------------------+
| 2025-01-24 10:38:34.589469 |
+----------------------------+
------------------------------------------------------
Stopped at Local Timestamp: 2025-01-24 10:38:34.589469
------------------------------------------------------
